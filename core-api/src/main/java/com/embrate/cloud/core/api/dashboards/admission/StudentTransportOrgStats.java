package com.embrate.cloud.core.api.dashboards.admission;

import com.embrate.cloud.core.api.dashboards.common.InstituteValue;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StudentTransportOrgStats {
	
	private final List<InstituteValue> transportAssignedStudents;
	private final int totalTransportAssignedStudents;
	private final List<InstituteValue> transportUnassignedStudents;
	private final int totalTransportUnassignedStudents;

	public StudentTransportOrgStats(List<InstituteValue> enrolledStudents, int totalEnrolledStudents,
									List<InstituteValue> transportAssignedStudents, int totalTransportAssignedStudents,
									List<InstituteValue> transportUnassignedStudents, int totalTransportUnassignedStudents) {
		this.enrolledStudents = enrolledStudents;
		this.totalEnrolledStudents = totalEnrolledStudents;
		this.transportAssignedStudents = transportAssignedStudents;
		this.totalTransportAssignedStudents = totalTransportAssignedStudents;
		this.transportUnassignedStudents = transportUnassignedStudents;
		this.totalTransportUnassignedStudents = totalTransportUnassignedStudents;
	}

	public List<InstituteValue> getEnrolledStudents() {
		return enrolledStudents;
	}

	public int getTotalEnrolledStudents() {
		return totalEnrolledStudents;
	}

	public List<InstituteValue> getTransportAssignedStudents() {
		return transportAssignedStudents;
	}

	public int getTotalTransportAssignedStudents() {
		return totalTransportAssignedStudents;
	}

	public List<InstituteValue> getTransportUnassignedStudents() {
		return transportUnassignedStudents;
	}

	public int getTotalTransportUnassignedStudents() {
		return totalTransportUnassignedStudents;
	}

	@Override
	public String toString() {
		return "StudentTransportOrgStats{" +
				"enrolledStudents=" + enrolledStudents +
				", totalEnrolledStudents=" + totalEnrolledStudents +
				", transportAssignedStudents=" + transportAssignedStudents +
				", totalTransportAssignedStudents=" + totalTransportAssignedStudents +
				", transportUnassignedStudents=" + transportUnassignedStudents +
				", totalTransportUnassignedStudents=" + totalTransportUnassignedStudents +
				'}';
	}
}
